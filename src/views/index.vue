<!-- eslint-disable -->
<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="6">
                <div class="card1-left">
                    <div class="card1-left-top">
                        <div class="card1-left-top-text">物理机</div>
                        <div style="display: flex;align-items: center;height: 100%;justify-content: space-between;">
                            <svg-icon 
                                icon-class="physical" 
                                class="menu-icon"
                            />
                            <div class="card1-left-top-num">9001</div> 
                        </div>
                    </div>
                    <div class="card1-left-bottom">
                        <div class="card1-left-bottom-text">虚拟机</div>
                        <div style="display: flex;align-items: center;height: 100%;justify-content: space-between;">
                            <svg-icon 
                                icon-class="virtual" 
                                class="menu-icon"
                            />
                            <div class="card1-left-bottom-num">8</div> 
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>
<script lang="ts" setup>

</script>
<style lang="scss" scoped>
.card1-left {
    width: 100%;
    .menu-icon {
        font-size: 54px;
        margin-right: 10px;
    }
    &-top {
        height: 120px;
        background: linear-gradient(to bottom, #94e7cb,#69bbd8);
        margin-bottom: 20px;
        border-radius: 10px;
        padding: 10px;
        &-text {
            font-size: 13px;
            color: #fff;
            font-weight: 600;
            letter-spacing: 2px;
        }
        &-num{
            color: #fff;
            font-size: 90px;
            font-weight: 600;
        }

    }
    &-bottom {
        height: 120px;
        background: linear-gradient(to bottom, #7fbeff,#637cee);
        border-radius: 10px;
        padding: 10px;
        
        &-text {
            font-size: 13px;
            color: #fff;
            font-weight: 600;
            letter-spacing: 2px;
        }
        &-num{
            color: #fff;
            font-size: 90px;
            font-weight: 600;
        }
    }
}
</style>