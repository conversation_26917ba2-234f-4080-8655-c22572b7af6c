<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as THREE from 'three'

// 响应式数据定义
const threeContainer = ref<HTMLElement>() // 3D场景容器的引用
let scene: THREE.Scene // Three.js场景对象
let camera: THREE.PerspectiveCamera // 透视相机对象
let renderer: THREE.WebGLRenderer // WebGL渲染器对象
let animationId: number // 动画帧ID，用于取消动画

// 机房监控数据
const serverData = ref({
  totalServers: 24, // 服务器总数
  activeServers: 22, // 活跃服务器数
  cpuUsage: 68, // CPU使用率
  memoryUsage: 72, // 内存使用率
  temperature: 23.5, // 机房温度
  humidity: 45, // 机房湿度
  powerConsumption: 15.8, // 功耗(kW)
  networkTraffic: 1.2 // 网络流量(GB/s)
})

// 服务器状态数据（模拟24台服务器）
const servers = ref(Array.from({ length: 24 }, (_, i) => ({
  id: i + 1,
  name: `Server-${String(i + 1).padStart(2, '0')}`,
  status: Math.random() > 0.1 ? 'active' : 'inactive', // 90%概率为活跃状态
  cpu: Math.floor(Math.random() * 100), // 随机CPU使用率
  memory: Math.floor(Math.random() * 100), // 随机内存使用率
  temperature: 20 + Math.random() * 15 // 20-35度随机温度
})))

/**
 * 初始化Three.js场景
 */
const initThreeJS = () => {
  if (!threeContainer.value) {
    console.error('Three.js容器未找到')
    return
  }

  console.log('开始初始化Three.js场景...')

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0x222222)

  // 获取容器尺寸
  const width = threeContainer.value.clientWidth
  const height = threeContainer.value.clientHeight

  console.log('容器尺寸:', width, 'x', height)

  // 创建相机
  camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
  camera.position.set(0, 10, 20)
  camera.lookAt(0, 0, 0)

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(width, height)
  renderer.setClearColor(0x222222)
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFShadowMap

  // 将渲染器添加到DOM
  threeContainer.value.appendChild(renderer.domElement)

  console.log('Three.js场景初始化完成')
}

/**
 * 创建机房地面
 */
const createFloor = () => {
  if (!scene) {
    console.error('场景未初始化')
    return
  }

  console.log('创建地面...')

  const floorGeometry = new THREE.PlaneGeometry(20, 15)
  const floorMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 })

  const floor = new THREE.Mesh(floorGeometry, floorMaterial)
  floor.rotation.x = -Math.PI / 2
  floor.position.y = 0
  floor.receiveShadow = true

  scene.add(floor)
  console.log('地面创建完成')
}

/**
 * 创建服务器机柜
 */
const createServers = () => {
  if (!scene) {
    console.error('场景未初始化')
    return
  }

  console.log('创建服务器机柜...')

  // 创建12台服务器（3行4列）
  for (let i = 0; i < 12; i++) {
    const row = Math.floor(i / 4)
    const col = i % 4

    const x = (col - 1.5) * 3
    const z = (row - 1) * 3

    const serverGeometry = new THREE.BoxGeometry(1.5, 2, 1)
    const isActive = Math.random() > 0.2
    const color = isActive ? 0x00ff88 : 0xff4444

    const serverMaterial = new THREE.MeshLambertMaterial({ color })
    const serverMesh = new THREE.Mesh(serverGeometry, serverMaterial)

    serverMesh.position.set(x, 1, z)
    serverMesh.castShadow = true

    scene.add(serverMesh)
  }

  console.log('所有服务器创建完成')
}

/**
 * 创建测试立方体
 */
const createTestCube = () => {
  if (!scene) return

  console.log('创建测试立方体...')

  const geometry = new THREE.BoxGeometry(2, 2, 2)
  const material = new THREE.MeshLambertMaterial({ color: 0x00ff00 })
  const cube = new THREE.Mesh(geometry, material)

  cube.position.set(0, 1, 0)
  cube.castShadow = true

  scene.add(cube)
  console.log('测试立方体创建完成')
}

/**
 * 创建照明系统
 */
const createLights = () => {
  if (!scene) {
    console.error('场景未初始化')
    return
  }

  console.log('创建光照系统...')

  // 创建环境光
  const ambientLight = new THREE.AmbientLight(0x404040, 0.8)
  scene.add(ambientLight)

  // 创建方向光
  const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
  directionalLight.position.set(10, 10, 5)
  directionalLight.castShadow = true

  // 设置阴影参数
  directionalLight.shadow.mapSize.width = 1024
  directionalLight.shadow.mapSize.height = 1024
  directionalLight.shadow.camera.near = 0.1
  directionalLight.shadow.camera.far = 50
  directionalLight.shadow.camera.left = -20
  directionalLight.shadow.camera.right = 20
  directionalLight.shadow.camera.top = 20
  directionalLight.shadow.camera.bottom = -20

  scene.add(directionalLight)

  console.log('光照系统创建完成')
}

/**
 * 渲染循环函数
 */
const animate = () => {
  if (!scene || !camera || !renderer) {
    console.error('Three.js对象未初始化')
    return
  }

  // 请求下一帧动画
  animationId = requestAnimationFrame(animate)

  // 让相机围绕场景中心旋转
  const time = Date.now() * 0.0003
  const radius = 25
  camera.position.x = Math.cos(time) * radius
  camera.position.z = Math.sin(time) * radius
  camera.position.y = 10
  camera.lookAt(0, 0, 0)

  // 渲染场景
  renderer.render(scene, camera)
}

/**
 * 处理窗口大小变化
 */
const handleResize = () => {
  if (!threeContainer.value || !camera || !renderer) return

  camera.aspect = threeContainer.value.clientWidth / threeContainer.value.clientHeight
  camera.updateProjectionMatrix()

  renderer.setSize(
    threeContainer.value.clientWidth,
    threeContainer.value.clientHeight
  )
}

/**
 * 模拟实时数据更新
 */
const updateData = () => {
  // 更新服务器数据
  servers.value.forEach(server => {
    server.cpu = Math.max(0, Math.min(100, server.cpu + (Math.random() - 0.5) * 10))
    server.memory = Math.max(0, Math.min(100, server.memory + (Math.random() - 0.5) * 8))
    server.temperature = Math.max(15, Math.min(40, server.temperature + (Math.random() - 0.5) * 2))
  })

  // 更新整体监控数据
  serverData.value.cpuUsage = Math.max(0, Math.min(100, serverData.value.cpuUsage + (Math.random() - 0.5) * 5))
  serverData.value.memoryUsage = Math.max(0, Math.min(100, serverData.value.memoryUsage + (Math.random() - 0.5) * 4))
  serverData.value.temperature = Math.max(18, Math.min(30, serverData.value.temperature + (Math.random() - 0.5) * 1))
  serverData.value.humidity = Math.max(30, Math.min(70, serverData.value.humidity + (Math.random() - 0.5) * 3))
  serverData.value.powerConsumption = Math.max(10, Math.min(25, serverData.value.powerConsumption + (Math.random() - 0.5) * 0.5))
  serverData.value.networkTraffic = Math.max(0.1, Math.min(5, serverData.value.networkTraffic + (Math.random() - 0.5) * 0.2))
}

// 组件挂载时的初始化
onMounted(() => {
  console.log('组件挂载，开始初始化3D场景...')

  // 等待DOM渲染完成
  setTimeout(() => {
    // 初始化Three.js场景
    initThreeJS()

    // 创建3D场景内容
    createFloor() // 创建地面
    createTestCube() // 创建测试立方体
    createServers() // 创建服务器
    createLights() // 创建照明

    // 开始动画循环
    animate()

    console.log('3D场景初始化完成，开始渲染...')
  }, 100)

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)

  // 设置数据更新定时器（每3秒更新一次）
  const dataUpdateInterval = setInterval(updateData, 3000)

  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(dataUpdateInterval)
  })
})

// 组件卸载时的清理工作
onUnmounted(() => {
  // 取消动画循环
  if (animationId) {
    cancelAnimationFrame(animationId)
  }

  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleResize)

  // 清理Three.js资源
  if (renderer) {
    renderer.dispose()
  }
})
</script>

<template>
  <!-- 3D可视化大屏容器 -->
  <div class="dashboard-container">
    <!-- 左侧监控面板 -->
    <div class="left-panel">
      <!-- 机房概览卡片 -->
      <div class="monitor-card">
        <h3 class="card-title">机房概览</h3>
        <div class="overview-grid">
          <div class="overview-item">
            <div class="item-label">服务器总数</div>
            <div class="item-value">{{ serverData.totalServers }}</div>
          </div>
          <div class="overview-item">
            <div class="item-label">活跃服务器</div>
            <div class="item-value active">{{ serverData.activeServers }}</div>
          </div>
        </div>
      </div>

      <!-- 系统性能卡片 -->
      <div class="monitor-card">
        <h3 class="card-title">系统性能</h3>
        <div class="performance-item">
          <div class="perf-label">CPU使用率</div>
          <div class="progress-bar">
            <div
              class="progress-fill cpu"
              :style="{ width: serverData.cpuUsage + '%' }"
            ></div>
          </div>
          <div class="perf-value">{{ serverData.cpuUsage }}%</div>
        </div>
        <div class="performance-item">
          <div class="perf-label">内存使用率</div>
          <div class="progress-bar">
            <div
              class="progress-fill memory"
              :style="{ width: serverData.memoryUsage + '%' }"
            ></div>
          </div>
          <div class="perf-value">{{ serverData.memoryUsage }}%</div>
        </div>
      </div>

      <!-- 环境监控卡片 -->
      <div class="monitor-card">
        <h3 class="card-title">环境监控</h3>
        <div class="env-grid">
          <div class="env-item">
            <div class="env-icon">🌡️</div>
            <div class="env-info">
              <div class="env-label">温度</div>
              <div class="env-value">{{ serverData.temperature }}°C</div>
            </div>
          </div>
          <div class="env-item">
            <div class="env-icon">💧</div>
            <div class="env-info">
              <div class="env-label">湿度</div>
              <div class="env-value">{{ serverData.humidity }}%</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资源消耗卡片 -->
      <div class="monitor-card">
        <h3 class="card-title">资源消耗</h3>
        <div class="resource-item">
          <div class="res-icon">⚡</div>
          <div class="res-info">
            <div class="res-label">功耗</div>
            <div class="res-value">{{ serverData.powerConsumption }} kW</div>
          </div>
        </div>
        <div class="resource-item">
          <div class="res-icon">🌐</div>
          <div class="res-info">
            <div class="res-label">网络流量</div>
            <div class="res-value">{{ serverData.networkTraffic }} GB/s</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间3D场景区域 -->
    <div class="center-panel">
      <div class="scene-header">
        <h2 class="scene-title">机房3D可视化</h2>
        <div class="scene-info">
          <span class="info-item">
            <span class="status-dot active"></span>
            活跃服务器
          </span>
          <span class="info-item">
            <span class="status-dot inactive"></span>
            离线服务器
          </span>
        </div>
      </div>
      <!-- Three.js 3D场景容器 -->
      <div ref="threeContainer" class="three-container"></div>
    </div>

    <!-- 右侧服务器列表面板 -->
    <div class="right-panel">
      <div class="monitor-card">
        <h3 class="card-title">服务器状态</h3>
        <div class="server-list">
          <div
            v-for="server in servers"
            :key="server.id"
            class="server-item"
            :class="{ active: server.status === 'active' }"
          >
            <div class="server-header">
              <div class="server-name">{{ server.name }}</div>
              <div
                class="server-status"
                :class="server.status"
              >
                {{ server.status === 'active' ? '运行中' : '离线' }}
              </div>
            </div>
            <div class="server-metrics">
              <div class="metric">
                <span class="metric-label">CPU:</span>
                <span class="metric-value">{{ server.cpu }}%</span>
              </div>
              <div class="metric">
                <span class="metric-label">内存:</span>
                <span class="metric-value">{{ server.memory }}%</span>
              </div>
              <div class="metric">
                <span class="metric-label">温度:</span>
                <span class="metric-value">{{ server.temperature.toFixed(1) }}°C</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 主容器样式 - 3D可视化大屏布局 */
.dashboard-container {
  display: flex; /* 使用弹性布局 */
  height: 100vh; /* 占满整个视口高度 */
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%); /* 深色渐变背景 */
  color: #ffffff; /* 白色文字 */
  font-family: 'Arial', sans-serif; /* 字体设置 */
  overflow: hidden; /* 隐藏溢出内容 */
}

/* 左侧监控面板样式 */
.left-panel {
  width: 300px; /* 固定宽度300px */
  padding: 20px; /* 内边距 */
  background: rgba(0, 0, 0, 0.3); /* 半透明黑色背景 */
  backdrop-filter: blur(10px); /* 背景模糊效果 */
  border-right: 1px solid rgba(255, 255, 255, 0.1); /* 右边框 */
  overflow-y: auto; /* 垂直滚动 */
}

/* 监控卡片通用样式 */
.monitor-card {
  background: rgba(255, 255, 255, 0.05); /* 半透明白色背景 */
  border: 1px solid rgba(255, 255, 255, 0.1); /* 边框 */
  border-radius: 12px; /* 圆角 */
  padding: 20px; /* 内边距 */
  margin-bottom: 20px; /* 底部外边距 */
  backdrop-filter: blur(5px); /* 背景模糊 */
  transition: all 0.3s ease; /* 过渡动画 */
}

.monitor-card:hover {
  background: rgba(255, 255, 255, 0.08); /* 悬停时背景变亮 */
  border-color: rgba(0, 255, 136, 0.3); /* 悬停时边框变绿 */
  transform: translateY(-2px); /* 悬停时轻微上移 */
}

/* 卡片标题样式 */
.card-title {
  margin: 0 0 15px 0; /* 外边距 */
  font-size: 18px; /* 字体大小 */
  font-weight: 600; /* 字体粗细 */
  color: #00ff88; /* 绿色标题 */
  text-align: center; /* 居中对齐 */
}

/* 概览网格布局 */
.overview-grid {
  display: grid; /* 网格布局 */
  grid-template-columns: 1fr 1fr; /* 两列等宽 */
  gap: 15px; /* 网格间距 */
}

.overview-item {
  text-align: center; /* 居中对齐 */
  padding: 10px; /* 内边距 */
  background: rgba(255, 255, 255, 0.03); /* 背景 */
  border-radius: 8px; /* 圆角 */
}

.item-label {
  font-size: 12px; /* 字体大小 */
  color: #cccccc; /* 灰色文字 */
  margin-bottom: 5px; /* 底部外边距 */
}

.item-value {
  font-size: 24px; /* 大字体 */
  font-weight: bold; /* 粗体 */
  color: #ffffff; /* 白色 */
}

.item-value.active {
  color: #00ff88; /* 活跃状态绿色 */
}

/* 性能监控样式 */
.performance-item {
  margin-bottom: 15px; /* 底部外边距 */
}

.perf-label {
  font-size: 14px; /* 字体大小 */
  color: #cccccc; /* 灰色文字 */
  margin-bottom: 8px; /* 底部外边距 */
}

/* 进度条样式 */
.progress-bar {
  width: 100%; /* 全宽 */
  height: 8px; /* 高度 */
  background: rgba(255, 255, 255, 0.1); /* 背景 */
  border-radius: 4px; /* 圆角 */
  overflow: hidden; /* 隐藏溢出 */
  margin-bottom: 5px; /* 底部外边距 */
}

.progress-fill {
  height: 100%; /* 全高 */
  border-radius: 4px; /* 圆角 */
  transition: width 0.5s ease; /* 宽度变化动画 */
}

.progress-fill.cpu {
  background: linear-gradient(90deg, #ff6b6b, #ff8e53); /* CPU进度条渐变色 */
}

.progress-fill.memory {
  background: linear-gradient(90deg, #4ecdc4, #44a08d); /* 内存进度条渐变色 */
}

.perf-value {
  font-size: 14px; /* 字体大小 */
  font-weight: bold; /* 粗体 */
  color: #ffffff; /* 白色 */
  text-align: right; /* 右对齐 */
}

/* 环境监控网格 */
.env-grid {
  display: grid; /* 网格布局 */
  grid-template-columns: 1fr 1fr; /* 两列 */
  gap: 15px; /* 间距 */
}

.env-item {
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  padding: 10px; /* 内边距 */
  background: rgba(255, 255, 255, 0.03); /* 背景 */
  border-radius: 8px; /* 圆角 */
}

.env-icon {
  font-size: 24px; /* 图标大小 */
  margin-right: 10px; /* 右外边距 */
}

.env-label {
  font-size: 12px; /* 字体大小 */
  color: #cccccc; /* 灰色 */
}

.env-value {
  font-size: 16px; /* 字体大小 */
  font-weight: bold; /* 粗体 */
  color: #00ff88; /* 绿色 */
}

/* 资源消耗样式 */
.resource-item {
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  padding: 12px; /* 内边距 */
  margin-bottom: 10px; /* 底部外边距 */
  background: rgba(255, 255, 255, 0.03); /* 背景 */
  border-radius: 8px; /* 圆角 */
}

.res-icon {
  font-size: 20px; /* 图标大小 */
  margin-right: 12px; /* 右外边距 */
}

.res-label {
  font-size: 14px; /* 字体大小 */
  color: #cccccc; /* 灰色 */
}

.res-value {
  font-size: 16px; /* 字体大小 */
  font-weight: bold; /* 粗体 */
  color: #ffffff; /* 白色 */
}

/* 中间3D场景面板 */
.center-panel {
  flex: 1; /* 占据剩余空间 */
  display: flex; /* 弹性布局 */
  flex-direction: column; /* 垂直方向 */
  position: relative; /* 相对定位 */
}

/* 场景头部 */
.scene-header {
  display: flex; /* 弹性布局 */
  justify-content: space-between; /* 两端对齐 */
  align-items: center; /* 垂直居中 */
  padding: 20px; /* 内边距 */
  background: rgba(0, 0, 0, 0.2); /* 半透明背景 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* 底边框 */
}

.scene-title {
  margin: 0; /* 无外边距 */
  font-size: 24px; /* 字体大小 */
  font-weight: 600; /* 字体粗细 */
  color: #00ff88; /* 绿色 */
}

.scene-info {
  display: flex; /* 弹性布局 */
  gap: 20px; /* 间距 */
}

.info-item {
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  font-size: 14px; /* 字体大小 */
  color: #cccccc; /* 灰色 */
}

/* 状态指示点 */
.status-dot {
  width: 8px; /* 宽度 */
  height: 8px; /* 高度 */
  border-radius: 50%; /* 圆形 */
  margin-right: 8px; /* 右外边距 */
}

.status-dot.active {
  background: #00ff88; /* 活跃状态绿色 */
  box-shadow: 0 0 10px rgba(0, 255, 136, 0.5); /* 发光效果 */
}

.status-dot.inactive {
  background: #ff4444; /* 离线状态红色 */
  box-shadow: 0 0 10px rgba(255, 68, 68, 0.5); /* 发光效果 */
}

/* Three.js容器样式 */
.three-container {
  flex: 1; /* 占据剩余空间 */
  position: relative; /* 相对定位 */
  overflow: hidden; /* 隐藏溢出 */
  border: 1px solid rgba(255, 255, 255, 0.1); /* 边框 */
  border-radius: 0 0 12px 12px; /* 底部圆角 */
}

/* 右侧服务器列表面板 */
.right-panel {
  width: 320px; /* 固定宽度 */
  padding: 20px; /* 内边距 */
  background: rgba(0, 0, 0, 0.3); /* 半透明背景 */
  backdrop-filter: blur(10px); /* 背景模糊 */
  border-left: 1px solid rgba(255, 255, 255, 0.1); /* 左边框 */
  overflow-y: auto; /* 垂直滚动 */
}

/* 服务器列表样式 */
.server-list {
  max-height: calc(100vh - 120px); /* 最大高度 */
  overflow-y: auto; /* 垂直滚动 */
}

/* 服务器项目样式 */
.server-item {
  background: rgba(255, 255, 255, 0.03); /* 背景 */
  border: 1px solid rgba(255, 255, 255, 0.1); /* 边框 */
  border-radius: 8px; /* 圆角 */
  padding: 12px; /* 内边距 */
  margin-bottom: 10px; /* 底部外边距 */
  transition: all 0.3s ease; /* 过渡动画 */
}

.server-item.active {
  border-color: rgba(0, 255, 136, 0.3); /* 活跃状态边框 */
  background: rgba(0, 255, 136, 0.05); /* 活跃状态背景 */
}

.server-item:hover {
  background: rgba(255, 255, 255, 0.08); /* 悬停背景 */
  transform: translateX(5px); /* 悬停时右移 */
}

/* 服务器头部信息 */
.server-header {
  display: flex; /* 弹性布局 */
  justify-content: space-between; /* 两端对齐 */
  align-items: center; /* 垂直居中 */
  margin-bottom: 8px; /* 底部外边距 */
}

.server-name {
  font-size: 14px; /* 字体大小 */
  font-weight: 600; /* 字体粗细 */
  color: #ffffff; /* 白色 */
}

.server-status {
  font-size: 12px; /* 字体大小 */
  padding: 2px 8px; /* 内边距 */
  border-radius: 12px; /* 圆角 */
  font-weight: 500; /* 字体粗细 */
}

.server-status.active {
  background: rgba(0, 255, 136, 0.2); /* 活跃状态背景 */
  color: #00ff88; /* 活跃状态文字 */
}

.server-status.inactive {
  background: rgba(255, 68, 68, 0.2); /* 离线状态背景 */
  color: #ff4444; /* 离线状态文字 */
}

/* 服务器指标样式 */
.server-metrics {
  display: grid; /* 网格布局 */
  grid-template-columns: 1fr 1fr 1fr; /* 三列等宽 */
  gap: 8px; /* 间距 */
}

.metric {
  text-align: center; /* 居中对齐 */
  font-size: 11px; /* 字体大小 */
}

.metric-label {
  color: #cccccc; /* 灰色标签 */
}

.metric-value {
  color: #ffffff; /* 白色数值 */
  font-weight: 600; /* 字体粗细 */
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar,
.server-list::-webkit-scrollbar {
  width: 6px; /* 滚动条宽度 */
}

.left-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track,
.server-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1); /* 滚动条轨道 */
  border-radius: 3px; /* 圆角 */
}

.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb,
.server-list::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 136, 0.5); /* 滚动条滑块 */
  border-radius: 3px; /* 圆角 */
}

.left-panel::-webkit-scrollbar-thumb:hover,
.right-panel::-webkit-scrollbar-thumb:hover,
.server-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 136, 0.7); /* 悬停时滑块颜色 */
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel,
  .right-panel {
    width: 250px; /* 小屏幕时减小侧边栏宽度 */
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column; /* 小屏幕时垂直布局 */
  }

  .left-panel,
  .right-panel {
    width: 100%; /* 全宽 */
    height: auto; /* 自动高度 */
  }

  .center-panel {
    height: 400px; /* 固定高度 */
  }
}
</style>