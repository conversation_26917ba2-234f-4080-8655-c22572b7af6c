<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as THREE from 'three'

// 响应式数据定义
const threeContainer = ref<HTMLElement>() // 3D场景容器的引用
const scene = ref<THREE.Scene>() // Three.js场景对象
const camera = ref<THREE.PerspectiveCamera>() // 透视相机对象
const renderer = ref<THREE.WebGLRenderer>() // WebGL渲染器对象
const animationId = ref<number>() // 动画帧ID，用于取消动画

// 机房监控数据
const serverData = ref({
  totalServers: 24, // 服务器总数
  activeServers: 22, // 活跃服务器数
  cpuUsage: 68, // CPU使用率
  memoryUsage: 72, // 内存使用率
  temperature: 23.5, // 机房温度
  humidity: 45, // 机房湿度
  powerConsumption: 15.8, // 功耗(kW)
  networkTraffic: 1.2 // 网络流量(GB/s)
})

// 服务器状态数据（模拟24台服务器）
const servers = ref(Array.from({ length: 24 }, (_, i) => ({
  id: i + 1,
  name: `Server-${String(i + 1).padStart(2, '0')}`,
  status: Math.random() > 0.1 ? 'active' : 'inactive', // 90%概率为活跃状态
  cpu: Math.floor(Math.random() * 100), // 随机CPU使用率
  memory: Math.floor(Math.random() * 100), // 随机内存使用率
  temperature: 20 + Math.random() * 15 // 20-35度随机温度
})))

/**
 * 初始化Three.js场景
 * 创建场景、相机、渲染器等基础对象
 */
const initThreeJS = () => {
  if (!threeContainer.value) return

  // 创建场景 - 所有3D对象的容器
  scene.value = new THREE.Scene()
  scene.value.background = new THREE.Color(0x0a0a0a) // 设置深色背景

  // 创建透视相机 - 模拟人眼观察3D世界的方式
  // 参数：视野角度(75度)，宽高比，近裁剪面(0.1)，远裁剪面(1000)
  camera.value = new THREE.PerspectiveCamera(
    75,
    threeContainer.value.clientWidth / threeContainer.value.clientHeight,
    0.1,
    1000
  )

  // 设置相机位置 - x轴右，y轴上，z轴向外
  camera.value.position.set(0, 15, 25)
  // 让相机看向场景中心
  camera.value.lookAt(0, 0, 0)

  // 创建WebGL渲染器 - 负责将3D场景渲染到2D屏幕上
  renderer.value = new THREE.WebGLRenderer({
    antialias: true, // 开启抗锯齿，让边缘更平滑
    alpha: true // 支持透明背景
  })

  // 设置渲染器尺寸为容器大小
  renderer.value.setSize(
    threeContainer.value.clientWidth,
    threeContainer.value.clientHeight
  )

  // 启用阴影渲染 - 让物体能够投射和接收阴影
  renderer.value.shadowMap.enabled = true
  renderer.value.shadowMap.type = THREE.PCFSoftShadowMap // 使用软阴影

  // 将渲染器的DOM元素添加到容器中
  threeContainer.value.appendChild(renderer.value.domElement)
}

/**
 * 创建机房地面
 * 使用平面几何体创建机房的地板
 */
const createFloor = () => {
  if (!scene.value) return

  // 创建平面几何体 - 宽30，高20
  const floorGeometry = new THREE.PlaneGeometry(30, 20)

  // 创建材质 - 使用深灰色，带有一定的粗糙度
  const floorMaterial = new THREE.MeshLambertMaterial({
    color: 0x333333, // 深灰色
    transparent: true,
    opacity: 0.8
  })

  // 创建地面网格对象
  const floor = new THREE.Mesh(floorGeometry, floorMaterial)

  // 旋转地面使其水平放置（默认是垂直的）
  floor.rotation.x = -Math.PI / 2
  floor.position.y = -1 // 稍微下沉一点

  // 设置地面可以接收阴影
  floor.receiveShadow = true

  // 将地面添加到场景中
  scene.value.add(floor)
}

/**
 * 创建机房墙壁
 * 创建四面墙壁围成机房空间
 */
const createWalls = () => {
  if (!scene.value) return

  // 墙壁材质 - 半透明的蓝灰色
  const wallMaterial = new THREE.MeshLambertMaterial({
    color: 0x2c3e50,
    transparent: true,
    opacity: 0.3
  })

  // 后墙
  const backWallGeometry = new THREE.PlaneGeometry(30, 12)
  const backWall = new THREE.Mesh(backWallGeometry, wallMaterial)
  backWall.position.set(0, 5, -10) // 位置：中心，高度5，后方10
  scene.value.add(backWall)

  // 左墙
  const leftWallGeometry = new THREE.PlaneGeometry(20, 12)
  const leftWall = new THREE.Mesh(leftWallGeometry, wallMaterial)
  leftWall.position.set(-15, 5, 0) // 位置：左侧15，高度5，中心
  leftWall.rotation.y = Math.PI / 2 // 旋转90度面向右侧
  scene.value.add(leftWall)

  // 右墙
  const rightWall = new THREE.Mesh(leftWallGeometry, wallMaterial)
  rightWall.position.set(15, 5, 0) // 位置：右侧15，高度5，中心
  rightWall.rotation.y = -Math.PI / 2 // 旋转-90度面向左侧
  scene.value.add(rightWall)
}

/**
 * 创建服务器机柜
 * 根据服务器数据创建3D服务器机柜模型
 */
const createServers = () => {
  if (!scene.value) return

  // 遍历所有服务器数据
  servers.value.forEach((server, index) => {
    // 计算服务器在机房中的位置
    // 6行4列的布局
    const row = Math.floor(index / 4) // 行号 (0-5)
    const col = index % 4 // 列号 (0-3)

    // 计算实际的3D坐标位置
    const x = (col - 1.5) * 6 // x坐标：-9, -3, 3, 9
    const z = (row - 2.5) * 3 // z坐标：从前到后排列

    // 创建服务器机柜的几何体 - 长方体
    const serverGeometry = new THREE.BoxGeometry(2, 4, 1.5)

    // 根据服务器状态选择颜色
    const color = server.status === 'active' ? 0x00ff88 : 0xff4444

    // 创建服务器材质
    const serverMaterial = new THREE.MeshLambertMaterial({
      color: color,
      transparent: true,
      opacity: 0.8
    })

    // 创建服务器网格对象
    const serverMesh = new THREE.Mesh(serverGeometry, serverMaterial)

    // 设置服务器位置
    serverMesh.position.set(x, 2, z) // y=2让服务器站在地面上

    // 设置服务器可以投射阴影
    serverMesh.castShadow = true

    // 将服务器添加到场景中
    if (scene.value) {
      scene.value.add(serverMesh)
    }

    // 创建服务器标签（显示服务器名称）
    createServerLabel(server.name, x, z + 1)
  })
}

/**
 * 创建服务器标签
 * 在服务器前方显示服务器名称
 */
const createServerLabel = (text: string, x: number, z: number) => {
  if (!scene.value) return

  // 创建画布用于绘制文字
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')!

  // 设置画布尺寸
  canvas.width = 256
  canvas.height = 64

  // 设置文字样式
  context.fillStyle = '#ffffff' // 白色文字
  context.font = '24px Arial' // 字体大小和类型
  context.textAlign = 'center' // 文字居中对齐

  // 在画布上绘制文字
  context.fillText(text, canvas.width / 2, canvas.height / 2 + 8)

  // 将画布转换为Three.js纹理
  const texture = new THREE.CanvasTexture(canvas)

  // 创建标签材质
  const labelMaterial = new THREE.MeshBasicMaterial({
    map: texture,
    transparent: true,
    alphaTest: 0.1 // 设置透明度测试，去除背景
  })

  // 创建标签几何体 - 小的平面
  const labelGeometry = new THREE.PlaneGeometry(2, 0.5)

  // 创建标签网格对象
  const label = new THREE.Mesh(labelGeometry, labelMaterial)

  // 设置标签位置（在服务器前方稍高的位置）
  label.position.set(x, 0.5, z)

  // 将标签添加到场景中
  scene.value.add(label)
}

/**
 * 创建照明系统
 * 添加环境光和聚光灯来照亮场景
 */
const createLights = () => {
  if (!scene.value) return

  // 创建环境光 - 提供整体的基础照明
  // 环境光会均匀地照亮场景中的所有物体
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6) // 暗灰色，强度0.6
  scene.value.add(ambientLight)

  // 创建聚光灯 - 提供定向照明和阴影
  const spotLight = new THREE.SpotLight(0xffffff, 1) // 白色，强度1

  // 设置聚光灯位置（上方偏后）
  spotLight.position.set(0, 20, 10)

  // 设置聚光灯照射目标（场景中心）
  spotLight.target.position.set(0, 0, 0)

  // 配置聚光灯阴影
  spotLight.castShadow = true // 启用阴影投射
  spotLight.shadow.mapSize.width = 2048 // 阴影贴图宽度
  spotLight.shadow.mapSize.height = 2048 // 阴影贴图高度
  spotLight.shadow.camera.near = 0.5 // 阴影相机近裁剪面
  spotLight.shadow.camera.far = 50 // 阴影相机远裁剪面

  // 将聚光灯和目标添加到场景中
  scene.value.add(spotLight)
  scene.value.add(spotLight.target)
}

/**
 * 渲染循环函数
 * 持续渲染场景，创建动画效果
 */
const animate = () => {
  if (!scene.value || !camera.value || !renderer.value) return

  // 请求下一帧动画
  animationId.value = requestAnimationFrame(animate)

  // 让相机围绕场景中心缓慢旋转，创建动态视角
  const time = Date.now() * 0.0005 // 获取时间用于动画
  camera.value.position.x = Math.cos(time) * 30 // x坐标随时间变化
  camera.value.position.z = Math.sin(time) * 30 // z坐标随时间变化
  camera.value.lookAt(0, 0, 0) // 始终看向场景中心

  // 渲染场景
  renderer.value.render(scene.value, camera.value)
}

/**
 * 处理窗口大小变化
 * 当浏览器窗口大小改变时，更新相机和渲染器
 */
const handleResize = () => {
  if (!threeContainer.value || !camera.value || !renderer.value) return

  // 更新相机宽高比
  camera.value.aspect = threeContainer.value.clientWidth / threeContainer.value.clientHeight
  camera.value.updateProjectionMatrix() // 更新投影矩阵

  // 更新渲染器尺寸
  renderer.value.setSize(
    threeContainer.value.clientWidth,
    threeContainer.value.clientHeight
  )
}

/**
 * 模拟实时数据更新
 * 定期更新服务器监控数据
 */
const updateData = () => {
  // 更新服务器数据
  servers.value.forEach(server => {
    // 随机更新CPU和内存使用率
    server.cpu = Math.max(0, Math.min(100, server.cpu + (Math.random() - 0.5) * 10))
    server.memory = Math.max(0, Math.min(100, server.memory + (Math.random() - 0.5) * 8))
    server.temperature = Math.max(15, Math.min(40, server.temperature + (Math.random() - 0.5) * 2))
  })

  // 更新整体监控数据
  serverData.value.cpuUsage = Math.max(0, Math.min(100, serverData.value.cpuUsage + (Math.random() - 0.5) * 5))
  serverData.value.memoryUsage = Math.max(0, Math.min(100, serverData.value.memoryUsage + (Math.random() - 0.5) * 4))
  serverData.value.temperature = Math.max(18, Math.min(30, serverData.value.temperature + (Math.random() - 0.5) * 1))
  serverData.value.humidity = Math.max(30, Math.min(70, serverData.value.humidity + (Math.random() - 0.5) * 3))
  serverData.value.powerConsumption = Math.max(10, Math.min(25, serverData.value.powerConsumption + (Math.random() - 0.5) * 0.5))
  serverData.value.networkTraffic = Math.max(0.1, Math.min(5, serverData.value.networkTraffic + (Math.random() - 0.5) * 0.2))
}

// 组件挂载时的初始化
onMounted(() => {
  // 初始化Three.js场景
  initThreeJS()

  // 创建3D场景内容
  createFloor() // 创建地面
  createWalls() // 创建墙壁
  createServers() // 创建服务器
  createLights() // 创建照明

  // 开始动画循环
  animate()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)

  // 设置数据更新定时器（每3秒更新一次）
  const dataUpdateInterval = setInterval(updateData, 3000)

  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(dataUpdateInterval)
  })
})

// 组件卸载时的清理工作
onUnmounted(() => {
  // 取消动画循环
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
  }

  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleResize)

  // 清理Three.js资源
  if (renderer.value) {
    renderer.value.dispose()
  }
})
</script>

<template>
  <!-- 3D可视化大屏容器 -->
  <div class="dashboard-container">
    <!-- 左侧监控面板 -->
    <div class="left-panel">
      <!-- 机房概览卡片 -->
      <div class="monitor-card">
        <h3 class="card-title">机房概览</h3>
        <div class="overview-grid">
          <div class="overview-item">
            <div class="item-label">服务器总数</div>
            <div class="item-value">{{ serverData.totalServers }}</div>
          </div>
          <div class="overview-item">
            <div class="item-label">活跃服务器</div>
            <div class="item-value active">{{ serverData.activeServers }}</div>
          </div>
        </div>
      </div>

      <!-- 系统性能卡片 -->
      <div class="monitor-card">
        <h3 class="card-title">系统性能</h3>
        <div class="performance-item">
          <div class="perf-label">CPU使用率</div>
          <div class="progress-bar">
            <div
              class="progress-fill cpu"
              :style="{ width: serverData.cpuUsage + '%' }"
            ></div>
          </div>
          <div class="perf-value">{{ serverData.cpuUsage }}%</div>
        </div>
        <div class="performance-item">
          <div class="perf-label">内存使用率</div>
          <div class="progress-bar">
            <div
              class="progress-fill memory"
              :style="{ width: serverData.memoryUsage + '%' }"
            ></div>
          </div>
          <div class="perf-value">{{ serverData.memoryUsage }}%</div>
        </div>
      </div>

      <!-- 环境监控卡片 -->
      <div class="monitor-card">
        <h3 class="card-title">环境监控</h3>
        <div class="env-grid">
          <div class="env-item">
            <div class="env-icon">🌡️</div>
            <div class="env-info">
              <div class="env-label">温度</div>
              <div class="env-value">{{ serverData.temperature }}°C</div>
            </div>
          </div>
          <div class="env-item">
            <div class="env-icon">💧</div>
            <div class="env-info">
              <div class="env-label">湿度</div>
              <div class="env-value">{{ serverData.humidity }}%</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资源消耗卡片 -->
      <div class="monitor-card">
        <h3 class="card-title">资源消耗</h3>
        <div class="resource-item">
          <div class="res-icon">⚡</div>
          <div class="res-info">
            <div class="res-label">功耗</div>
            <div class="res-value">{{ serverData.powerConsumption }} kW</div>
          </div>
        </div>
        <div class="resource-item">
          <div class="res-icon">🌐</div>
          <div class="res-info">
            <div class="res-label">网络流量</div>
            <div class="res-value">{{ serverData.networkTraffic }} GB/s</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间3D场景区域 -->
    <div class="center-panel">
      <div class="scene-header">
        <h2 class="scene-title">机房3D可视化</h2>
        <div class="scene-info">
          <span class="info-item">
            <span class="status-dot active"></span>
            活跃服务器
          </span>
          <span class="info-item">
            <span class="status-dot inactive"></span>
            离线服务器
          </span>
        </div>
      </div>
      <!-- Three.js 3D场景容器 -->
      <div ref="threeContainer" class="three-container"></div>
    </div>

    <!-- 右侧服务器列表面板 -->
    <div class="right-panel">
      <div class="monitor-card">
        <h3 class="card-title">服务器状态</h3>
        <div class="server-list">
          <div
            v-for="server in servers"
            :key="server.id"
            class="server-item"
            :class="{ active: server.status === 'active' }"
          >
            <div class="server-header">
              <div class="server-name">{{ server.name }}</div>
              <div
                class="server-status"
                :class="server.status"
              >
                {{ server.status === 'active' ? '运行中' : '离线' }}
              </div>
            </div>
            <div class="server-metrics">
              <div class="metric">
                <span class="metric-label">CPU:</span>
                <span class="metric-value">{{ server.cpu }}%</span>
              </div>
              <div class="metric">
                <span class="metric-label">内存:</span>
                <span class="metric-value">{{ server.memory }}%</span>
              </div>
              <div class="metric">
                <span class="metric-label">温度:</span>
                <span class="metric-value">{{ server.temperature.toFixed(1) }}°C</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 主容器样式 - 3D可视化大屏布局 */
.dashboard-container {
  display: flex; /* 使用弹性布局 */
  height: 100vh; /* 占满整个视口高度 */
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%); /* 深色渐变背景 */
  color: #ffffff; /* 白色文字 */
  font-family: 'Arial', sans-serif; /* 字体设置 */
  overflow: hidden; /* 隐藏溢出内容 */
}

/* 左侧监控面板样式 */
.left-panel {
  width: 300px; /* 固定宽度300px */
  padding: 20px; /* 内边距 */
  background: rgba(0, 0, 0, 0.3); /* 半透明黑色背景 */
  backdrop-filter: blur(10px); /* 背景模糊效果 */
  border-right: 1px solid rgba(255, 255, 255, 0.1); /* 右边框 */
  overflow-y: auto; /* 垂直滚动 */
}

/* 监控卡片通用样式 */
.monitor-card {
  background: rgba(255, 255, 255, 0.05); /* 半透明白色背景 */
  border: 1px solid rgba(255, 255, 255, 0.1); /* 边框 */
  border-radius: 12px; /* 圆角 */
  padding: 20px; /* 内边距 */
  margin-bottom: 20px; /* 底部外边距 */
  backdrop-filter: blur(5px); /* 背景模糊 */
  transition: all 0.3s ease; /* 过渡动画 */
}

.monitor-card:hover {
  background: rgba(255, 255, 255, 0.08); /* 悬停时背景变亮 */
  border-color: rgba(0, 255, 136, 0.3); /* 悬停时边框变绿 */
  transform: translateY(-2px); /* 悬停时轻微上移 */
}

/* 卡片标题样式 */
.card-title {
  margin: 0 0 15px 0; /* 外边距 */
  font-size: 18px; /* 字体大小 */
  font-weight: 600; /* 字体粗细 */
  color: #00ff88; /* 绿色标题 */
  text-align: center; /* 居中对齐 */
}

/* 概览网格布局 */
.overview-grid {
  display: grid; /* 网格布局 */
  grid-template-columns: 1fr 1fr; /* 两列等宽 */
  gap: 15px; /* 网格间距 */
}

.overview-item {
  text-align: center; /* 居中对齐 */
  padding: 10px; /* 内边距 */
  background: rgba(255, 255, 255, 0.03); /* 背景 */
  border-radius: 8px; /* 圆角 */
}

.item-label {
  font-size: 12px; /* 字体大小 */
  color: #cccccc; /* 灰色文字 */
  margin-bottom: 5px; /* 底部外边距 */
}

.item-value {
  font-size: 24px; /* 大字体 */
  font-weight: bold; /* 粗体 */
  color: #ffffff; /* 白色 */
}

.item-value.active {
  color: #00ff88; /* 活跃状态绿色 */
}

/* 性能监控样式 */
.performance-item {
  margin-bottom: 15px; /* 底部外边距 */
}

.perf-label {
  font-size: 14px; /* 字体大小 */
  color: #cccccc; /* 灰色文字 */
  margin-bottom: 8px; /* 底部外边距 */
}

/* 进度条样式 */
.progress-bar {
  width: 100%; /* 全宽 */
  height: 8px; /* 高度 */
  background: rgba(255, 255, 255, 0.1); /* 背景 */
  border-radius: 4px; /* 圆角 */
  overflow: hidden; /* 隐藏溢出 */
  margin-bottom: 5px; /* 底部外边距 */
}

.progress-fill {
  height: 100%; /* 全高 */
  border-radius: 4px; /* 圆角 */
  transition: width 0.5s ease; /* 宽度变化动画 */
}

.progress-fill.cpu {
  background: linear-gradient(90deg, #ff6b6b, #ff8e53); /* CPU进度条渐变色 */
}

.progress-fill.memory {
  background: linear-gradient(90deg, #4ecdc4, #44a08d); /* 内存进度条渐变色 */
}

.perf-value {
  font-size: 14px; /* 字体大小 */
  font-weight: bold; /* 粗体 */
  color: #ffffff; /* 白色 */
  text-align: right; /* 右对齐 */
}

/* 环境监控网格 */
.env-grid {
  display: grid; /* 网格布局 */
  grid-template-columns: 1fr 1fr; /* 两列 */
  gap: 15px; /* 间距 */
}

.env-item {
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  padding: 10px; /* 内边距 */
  background: rgba(255, 255, 255, 0.03); /* 背景 */
  border-radius: 8px; /* 圆角 */
}

.env-icon {
  font-size: 24px; /* 图标大小 */
  margin-right: 10px; /* 右外边距 */
}

.env-label {
  font-size: 12px; /* 字体大小 */
  color: #cccccc; /* 灰色 */
}

.env-value {
  font-size: 16px; /* 字体大小 */
  font-weight: bold; /* 粗体 */
  color: #00ff88; /* 绿色 */
}

/* 资源消耗样式 */
.resource-item {
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  padding: 12px; /* 内边距 */
  margin-bottom: 10px; /* 底部外边距 */
  background: rgba(255, 255, 255, 0.03); /* 背景 */
  border-radius: 8px; /* 圆角 */
}

.res-icon {
  font-size: 20px; /* 图标大小 */
  margin-right: 12px; /* 右外边距 */
}

.res-label {
  font-size: 14px; /* 字体大小 */
  color: #cccccc; /* 灰色 */
}

.res-value {
  font-size: 16px; /* 字体大小 */
  font-weight: bold; /* 粗体 */
  color: #ffffff; /* 白色 */
}

/* 中间3D场景面板 */
.center-panel {
  flex: 1; /* 占据剩余空间 */
  display: flex; /* 弹性布局 */
  flex-direction: column; /* 垂直方向 */
  position: relative; /* 相对定位 */
}

/* 场景头部 */
.scene-header {
  display: flex; /* 弹性布局 */
  justify-content: space-between; /* 两端对齐 */
  align-items: center; /* 垂直居中 */
  padding: 20px; /* 内边距 */
  background: rgba(0, 0, 0, 0.2); /* 半透明背景 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* 底边框 */
}

.scene-title {
  margin: 0; /* 无外边距 */
  font-size: 24px; /* 字体大小 */
  font-weight: 600; /* 字体粗细 */
  color: #00ff88; /* 绿色 */
}

.scene-info {
  display: flex; /* 弹性布局 */
  gap: 20px; /* 间距 */
}

.info-item {
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  font-size: 14px; /* 字体大小 */
  color: #cccccc; /* 灰色 */
}

/* 状态指示点 */
.status-dot {
  width: 8px; /* 宽度 */
  height: 8px; /* 高度 */
  border-radius: 50%; /* 圆形 */
  margin-right: 8px; /* 右外边距 */
}

.status-dot.active {
  background: #00ff88; /* 活跃状态绿色 */
  box-shadow: 0 0 10px rgba(0, 255, 136, 0.5); /* 发光效果 */
}

.status-dot.inactive {
  background: #ff4444; /* 离线状态红色 */
  box-shadow: 0 0 10px rgba(255, 68, 68, 0.5); /* 发光效果 */
}

/* Three.js容器样式 */
.three-container {
  flex: 1; /* 占据剩余空间 */
  position: relative; /* 相对定位 */
  overflow: hidden; /* 隐藏溢出 */
  border: 1px solid rgba(255, 255, 255, 0.1); /* 边框 */
  border-radius: 0 0 12px 12px; /* 底部圆角 */
}

/* 右侧服务器列表面板 */
.right-panel {
  width: 320px; /* 固定宽度 */
  padding: 20px; /* 内边距 */
  background: rgba(0, 0, 0, 0.3); /* 半透明背景 */
  backdrop-filter: blur(10px); /* 背景模糊 */
  border-left: 1px solid rgba(255, 255, 255, 0.1); /* 左边框 */
  overflow-y: auto; /* 垂直滚动 */
}

/* 服务器列表样式 */
.server-list {
  max-height: calc(100vh - 120px); /* 最大高度 */
  overflow-y: auto; /* 垂直滚动 */
}

/* 服务器项目样式 */
.server-item {
  background: rgba(255, 255, 255, 0.03); /* 背景 */
  border: 1px solid rgba(255, 255, 255, 0.1); /* 边框 */
  border-radius: 8px; /* 圆角 */
  padding: 12px; /* 内边距 */
  margin-bottom: 10px; /* 底部外边距 */
  transition: all 0.3s ease; /* 过渡动画 */
}

.server-item.active {
  border-color: rgba(0, 255, 136, 0.3); /* 活跃状态边框 */
  background: rgba(0, 255, 136, 0.05); /* 活跃状态背景 */
}

.server-item:hover {
  background: rgba(255, 255, 255, 0.08); /* 悬停背景 */
  transform: translateX(5px); /* 悬停时右移 */
}

/* 服务器头部信息 */
.server-header {
  display: flex; /* 弹性布局 */
  justify-content: space-between; /* 两端对齐 */
  align-items: center; /* 垂直居中 */
  margin-bottom: 8px; /* 底部外边距 */
}

.server-name {
  font-size: 14px; /* 字体大小 */
  font-weight: 600; /* 字体粗细 */
  color: #ffffff; /* 白色 */
}

.server-status {
  font-size: 12px; /* 字体大小 */
  padding: 2px 8px; /* 内边距 */
  border-radius: 12px; /* 圆角 */
  font-weight: 500; /* 字体粗细 */
}

.server-status.active {
  background: rgba(0, 255, 136, 0.2); /* 活跃状态背景 */
  color: #00ff88; /* 活跃状态文字 */
}

.server-status.inactive {
  background: rgba(255, 68, 68, 0.2); /* 离线状态背景 */
  color: #ff4444; /* 离线状态文字 */
}

/* 服务器指标样式 */
.server-metrics {
  display: grid; /* 网格布局 */
  grid-template-columns: 1fr 1fr 1fr; /* 三列等宽 */
  gap: 8px; /* 间距 */
}

.metric {
  text-align: center; /* 居中对齐 */
  font-size: 11px; /* 字体大小 */
}

.metric-label {
  color: #cccccc; /* 灰色标签 */
}

.metric-value {
  color: #ffffff; /* 白色数值 */
  font-weight: 600; /* 字体粗细 */
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar,
.server-list::-webkit-scrollbar {
  width: 6px; /* 滚动条宽度 */
}

.left-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track,
.server-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1); /* 滚动条轨道 */
  border-radius: 3px; /* 圆角 */
}

.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb,
.server-list::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 136, 0.5); /* 滚动条滑块 */
  border-radius: 3px; /* 圆角 */
}

.left-panel::-webkit-scrollbar-thumb:hover,
.right-panel::-webkit-scrollbar-thumb:hover,
.server-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 136, 0.7); /* 悬停时滑块颜色 */
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel,
  .right-panel {
    width: 250px; /* 小屏幕时减小侧边栏宽度 */
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column; /* 小屏幕时垂直布局 */
  }

  .left-panel,
  .right-panel {
    width: 100%; /* 全宽 */
    height: auto; /* 自动高度 */
  }

  .center-panel {
    height: 400px; /* 固定高度 */
  }
}
</style>