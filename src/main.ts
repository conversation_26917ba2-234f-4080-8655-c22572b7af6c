/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2025-04-11 15:36:40
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2025-04-11 16:41:54
 * @FilePath: \my-vue3-project\src\main.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from "./router"
import ElementPlus from "element-plus"
import "element-plus/dist/index.css"

const app = createApp(App)
app.use(router)
app.use(ElementPlus)
app.mount('#app')
