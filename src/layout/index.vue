<script setup lang="ts">
import Sidebar from "../layout/components/Sidebar.vue"
import Navbar from "../layout/components/Navbar.vue"
</script>
<template>
    <div class="classObj">
        <div class="classObj-left">
            <Sidebar />
        </div>
        <div class="classObj-right">
            <Navbar />
            <router-view />
        </div>
    </div>
</template>
<style scoped>
.classObj {
    height: 100%;
    display: grid;
    grid-template-columns: 200px 1fr;
}

.classObj-right {
    display: grid;
    grid-template-rows: 46px 1fr;
}
</style>