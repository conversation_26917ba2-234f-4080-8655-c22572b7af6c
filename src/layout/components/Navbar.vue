<script setup lang="ts">
import { Fold } from '@element-plus/icons-vue'
</script>
<template>
    <div class="right-top">
        <el-icon size="27" color="black" style="margin-right: 30px;margin-left: 10px;">
            <Fold />
        </el-icon>
    </div>
</template>
<style scoped>
.right-top {
    width: 100%;
    display: flex;
    align-items: center;
    height: 45px;
    border-bottom: 1px solid #e6e6e6;
}
.el-button.is-link {
    background: transparent;
    border-color: transparent;
    color: #000000;
    height: auto;
    padding: 2px;
}
</style>