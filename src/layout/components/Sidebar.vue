<script setup lang="ts">
import { ElMenu } from "element-plus"
import {
  Menu as IconMenu,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router';
const router = useRouter()
const menuList = [
    { name: '实时数据看板', path: '/', index: 'portal' },
    { name: '地理信息可视化', path: '/geography', index: 'topo' },
    { name: '3D可视化', path: '/threeD', index: 'serverTree' },
    { name: '大屏', path: '/screen', index: 'screen' },
]
const handleOpen = (key: string, keyPath: string[]) => {
    const item = menuList.find(i => i.index == key)
    router.push(item?.path || '/')
};
</script>
<template>
    <div class="sidebar">
        <div class="logo">OneOps</div>
        <el-menu
            class="el-menu-vertical-demo"
            @select="handleOpen"
            default-active="portal"
        >
            <el-menu-item 
                v-for="item in menuList" 
                :key="item.name" 
                :index="item.index"
            >
                <el-icon><icon-menu /></el-icon>
                {{ item.name }}
            </el-menu-item>
        </el-menu>
    </div>
</template>
<style scoped>
.sidebar{
    width: 200px;
}
.logo {
    line-height: 45px;
    text-align: center;
    font-weight: 600;
    font-size: 22px;
    border-bottom: 1px solid #e6e6e6;
}
.el-menu-vertical-demo {
    height: 95%;
}
</style>