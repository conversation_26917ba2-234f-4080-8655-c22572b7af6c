/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2025-04-11 16:38:56
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2025-04-11 17:55:10
 * @FilePath: \my-vue3-project\src\router\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createRouter, createWebHistory } from "vue-router";
import Layout from "../layout/index.vue";
// 定义路由
const routes = [
  {
    path: "/", // 根路径
    name: "Layout", // 布局组件
    component: Layout, // 布局组件始终存在
    children: [
      {
        path: "", // 默认子路由
        component: () => import("../views/index.vue"), // 首页内容
      },
      {
        path: "geography", // 默认子路由
        component: () => import("../views/geography/index.vue"), // 首页内容
      },
      {
        path: "threeD", // 默认子路由
        component: () => import("../views/threeD/index.vue"), // 首页内容
      },
      {
        path: "screen", // 默认子路由
        component: () => import("../views/largeScreen/index.vue"), // 首页内容
      },
    ],
  },
];
// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
});
export default router;
