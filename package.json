{"name": "my-vue3-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"element-plus": "^2.9.7", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "sass-embedded": "^1.86.3", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}