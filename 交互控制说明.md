# 3D机房可视化 - 交互控制说明

## 🖱️ 鼠标控制功能

### 1. **旋转视角** (左键拖拽)
- **操作**: 按住鼠标左键并拖拽
- **效果**: 围绕机房中心旋转相机视角
- **用途**: 从不同角度观察机房布局和服务器状态

### 2. **平移视角** (右键拖拽)
- **操作**: 按住鼠标右键并拖拽
- **效果**: 平移相机位置，改变观察的中心点
- **用途**: 移动到机房的不同区域进行观察

### 3. **缩放视角** (滚轮)
- **操作**: 滚动鼠标滚轮
- **效果**: 拉近或拉远相机距离
- **限制**: 最近距离5个单位，最远距离50个单位
- **用途**: 查看整体布局或观察细节

## 🎯 **控制器特性**

### **平滑阻尼**
- 启用了阻尼效果，让控制更加平滑自然
- 拖拽停止后会有惯性滑动效果
- 阻尼系数设置为0.05，提供舒适的操作体验

### **视角限制**
- **垂直角度限制**: 防止相机翻转到地面下方
- **缩放距离限制**: 防止相机过近或过远
- **目标点设置**: 相机围绕机房中心上方2个单位的点旋转

### **初始视角**
- **初始位置**: (15, 12, 15) - 斜角俯视角度
- **观察目标**: (0, 2, 0) - 机房中心稍微抬高的位置
- **最佳观察**: 可以同时看到地面、服务器和整体布局

## 📱 **页面布局**

### **控制提示**
在3D场景上方显示操作提示：
- 🖱️ 左键拖拽：旋转视角
- 🖱️ 右键拖拽：平移视角  
- 🖱️ 滚轮：缩放

### **状态指示**
- 🟢 绿色圆点：活跃服务器
- 🔴 红色圆点：离线服务器

## 🎨 **视觉反馈**

### **提示样式**
- 半透明背景的提示框
- 悬停时高亮显示
- 绿色主题色彩搭配

### **3D场景**
- 深灰色背景便于观察
- 实时阴影效果
- 平滑的材质渲染

## 🔧 **技术实现**

### **OrbitControls配置**
```typescript
// 启用阻尼效果
controls.enableDamping = true
controls.dampingFactor = 0.05

// 禁用屏幕空间平移
controls.screenSpacePanning = false

// 设置距离限制
controls.minDistance = 5
controls.maxDistance = 50

// 限制垂直角度
controls.maxPolarAngle = Math.PI / 2

// 设置旋转中心
controls.target.set(0, 2, 0)
```

### **渲染循环**
- 每帧更新控制器状态
- 处理阻尼效果
- 实时渲染场景

### **响应式处理**
- 窗口大小变化时自动调整
- 相机宽高比自适应
- 渲染器尺寸同步更新

## 🚀 **使用建议**

### **最佳观察角度**
1. **整体布局**: 缩小视角，从高处俯视
2. **服务器详情**: 拉近距离，平移到感兴趣的区域
3. **状态监控**: 旋转视角，从不同角度观察服务器状态

### **操作技巧**
1. **平滑操作**: 利用阻尼效果，轻柔地拖拽和滚动
2. **组合使用**: 结合旋转、平移、缩放获得最佳视角
3. **重置视角**: 刷新页面可以回到初始视角

### **性能优化**
- 控制器更新频率已优化
- 阻尼效果减少不必要的渲染
- 平滑的用户体验

## 📊 **与监控数据结合**

### **实时观察**
- 一边拖拽查看3D场景
- 一边观察左右两侧的监控数据
- 数据每3秒自动更新

### **状态关联**
- 3D场景中的服务器颜色与右侧列表状态对应
- 绿色服务器表示运行正常
- 红色服务器表示需要关注

## 🎯 **展示效果**

这种交互式的3D可视化系统非常适合：
- **技术演示**: 展示3D开发能力和用户体验设计
- **实际应用**: 数据中心监控和管理
- **学习参考**: Three.js和Vue3结合的最佳实践

通过鼠标交互，用户可以自由探索3D机房环境，获得沉浸式的监控体验！
